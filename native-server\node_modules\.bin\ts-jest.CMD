@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\ts-jest@29.3.4_@babel+core@_c36c044b91565ec7e4cf126b17b044a4\node_modules\ts-jest\node_modules;D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\ts-jest@29.3.4_@babel+core@_c36c044b91565ec7e4cf126b17b044a4\node_modules;D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\ts-jest@29.3.4_@babel+core@_c36c044b91565ec7e4cf126b17b044a4\node_modules\ts-jest\node_modules;D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\ts-jest@29.3.4_@babel+core@_c36c044b91565ec7e4cf126b17b044a4\node_modules;D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\ts-jest\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\ts-jest\cli.js" %*
)
