{"compilerOptions": {"target": "ES2018", "module": "NodeNext", "moduleResolution": "NodeNext", "lib": ["ES2018", "DOM"], "outDir": "dist", "rootDir": "src", "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "declaration": true, "sourceMap": true, "resolveJsonModule": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"]}