@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\ts-node@10.9.2_@types+node@22.15.30_typescript@5.8.3\node_modules\ts-node\dist\node_modules;D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\ts-node@10.9.2_@types+node@22.15.30_typescript@5.8.3\node_modules\ts-node\node_modules;D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\ts-node@10.9.2_@types+node@22.15.30_typescript@5.8.3\node_modules;D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\ts-node@10.9.2_@types+node@22.15.30_typescript@5.8.3\node_modules\ts-node\dist\node_modules;D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\ts-node@10.9.2_@types+node@22.15.30_typescript@5.8.3\node_modules\ts-node\node_modules;D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\ts-node@10.9.2_@types+node@22.15.30_typescript@5.8.3\node_modules;D:\chrome-exts\mcp-chrome-master\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\ts-node\dist\bin-esm.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\ts-node\dist\bin-esm.js" %*
)
